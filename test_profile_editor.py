#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
微博资料修改工具测试脚本
用于测试程序的基本功能
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    import tkinter as tk
    from tkinter import ttk
    print("✅ tkinter 模块导入成功")
except ImportError as e:
    print(f"❌ tkinter 模块导入失败: {e}")
    sys.exit(1)

try:
    import requests
    print("✅ requests 模块导入成功")
except ImportError as e:
    print(f"❌ requests 模块导入失败: {e}")
    print("请运行: pip install requests")
    sys.exit(1)

try:
    import json
    print("✅ json 模块导入成功")
except ImportError as e:
    print(f"❌ json 模块导入失败: {e}")
    sys.exit(1)

try:
    import threading
    print("✅ threading 模块导入成功")
except ImportError as e:
    print(f"❌ threading 模块导入失败: {e}")
    sys.exit(1)

try:
    import time
    print("✅ time 模块导入成功")
except ImportError as e:
    print(f"❌ time 模块导入失败: {e}")
    sys.exit(1)

try:
    import hashlib
    print("✅ hashlib 模块导入成功")
except ImportError as e:
    print(f"❌ hashlib 模块导入失败: {e}")
    sys.exit(1)

print("\n" + "="*50)
print("所有依赖模块检查完成！")
print("="*50)

# 测试主程序导入
try:
    from weibo_profile_editor import WeiboProfileEditor, AccountInfo
    print("✅ 主程序模块导入成功")
except ImportError as e:
    print(f"❌ 主程序模块导入失败: {e}")
    sys.exit(1)

# 测试基本功能
def test_account_info():
    """测试AccountInfo类"""
    print("\n🧪 测试AccountInfo类...")
    
    account = AccountInfo("123456", "test_gsid", "test_sub", "test_subp")
    
    assert account.uid == "123456"
    assert account.gsid == "test_gsid"
    assert account.sub_cookie == "test_sub"
    assert account.subp_cookie == "test_subp"
    assert account.status == "待处理"
    
    print("✅ AccountInfo类测试通过")

def test_gui_creation():
    """测试GUI创建"""
    print("\n🧪 测试GUI创建...")
    
    try:
        root = tk.Tk()
        root.withdraw()  # 隐藏窗口
        
        app = WeiboProfileEditor(root)
        
        # 测试基本属性
        assert hasattr(app, 'accounts')
        assert hasattr(app, 'current_account_index')
        assert hasattr(app, 'log_message')
        
        root.destroy()
        print("✅ GUI创建测试通过")
        
    except Exception as e:
        print(f"❌ GUI创建测试失败: {e}")
        return False
    
    return True

def test_account_parsing():
    """测试账号解析功能"""
    print("\n🧪 测试账号解析功能...")
    
    try:
        root = tk.Tk()
        root.withdraw()
        
        app = WeiboProfileEditor(root)
        
        # 模拟输入账号信息
        test_data = "123456----test_gsid----test_sub----test_subp\n789012----test_gsid2----test_sub2----test_subp2"
        app.account_input.insert(1.0, test_data)
        
        # 解析账号（不执行网络请求部分）
        original_fetch = app.fetch_account_details
        app.fetch_account_details = lambda: None  # 禁用网络请求
        
        app.parse_accounts()
        
        # 检查解析结果
        assert len(app.accounts) == 2
        assert app.accounts[0].uid == "123456"
        assert app.accounts[1].uid == "789012"
        
        root.destroy()
        print("✅ 账号解析功能测试通过")
        
    except Exception as e:
        print(f"❌ 账号解析功能测试失败: {e}")
        return False
    
    return True

def main():
    """主测试函数"""
    print("🚀 开始测试微博资料修改工具...")
    
    # 运行测试
    test_account_info()
    
    if not test_gui_creation():
        return False
    
    if not test_account_parsing():
        return False
    
    print("\n" + "="*50)
    print("🎉 所有测试通过！程序可以正常运行。")
    print("="*50)
    
    # 询问是否启动主程序
    try:
        choice = input("\n是否启动主程序？(y/n): ").lower().strip()
        if choice in ['y', 'yes', '是']:
            print("正在启动主程序...")
            from weibo_profile_editor import main
            main()
    except KeyboardInterrupt:
        print("\n程序已退出")
    
    return True

if __name__ == "__main__":
    main()
