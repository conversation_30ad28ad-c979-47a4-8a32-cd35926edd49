#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试程序启动
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """测试模块导入"""
    print("🧪 测试模块导入...")
    
    try:
        import tkinter as tk
        print("✅ tkinter 导入成功")
    except ImportError as e:
        print(f"❌ tkinter 导入失败: {e}")
        return False
    
    try:
        import requests
        print("✅ requests 导入成功")
    except ImportError as e:
        print(f"❌ requests 导入失败: {e}")
        print("请运行: pip install requests")
        return False
    
    try:
        from weibo_profile_editor import WeiboProfileEditor, AccountInfo
        print("✅ 主程序模块导入成功")
    except ImportError as e:
        print(f"❌ 主程序模块导入失败: {e}")
        return False
    
    return True

def test_gui():
    """测试GUI创建"""
    print("\n🧪 测试GUI创建...")
    
    try:
        import tkinter as tk
        from weibo_profile_editor import WeiboProfileEditor
        
        root = tk.Tk()
        root.withdraw()  # 隐藏窗口
        
        app = WeiboProfileEditor(root)
        
        # 检查基本属性
        assert hasattr(app, 'accounts')
        assert hasattr(app, 'current_account_index')
        assert hasattr(app, 'selected_avatar_path')
        
        root.destroy()
        print("✅ GUI创建成功")
        return True
        
    except Exception as e:
        print(f"❌ GUI创建失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试微博资料修改工具...")
    
    if not test_imports():
        print("\n❌ 模块导入测试失败")
        return False
    
    if not test_gui():
        print("\n❌ GUI测试失败")
        return False
    
    print("\n✅ 所有测试通过！")
    print("="*50)
    print("程序功能说明：")
    print("1. 支持修改性别、昵称、简介、头像")
    print("2. 账号格式：UID----GSID")
    print("3. 自动获取AID和S值")
    print("4. 支持批量账号管理")
    print("="*50)
    
    # 询问是否启动主程序
    try:
        choice = input("\n是否启动主程序？(y/n): ").lower().strip()
        if choice in ['y', 'yes', '是']:
            print("正在启动主程序...")
            from weibo_profile_editor import main
            main()
    except KeyboardInterrupt:
        print("\n程序已退出")
    
    return True

if __name__ == "__main__":
    main()
