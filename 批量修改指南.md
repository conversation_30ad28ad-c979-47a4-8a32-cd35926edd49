# 微博资料批量修改指南

## 🎯 批量修改功能概述

新版本支持一次性修改大量账号的资料，包括性别、昵称、头像和简介。你可以：
- 导入N个账号
- 导入N个昵称（按顺序分配）
- 选择头像文件夹（随机分配）
- 设置统一的性别和简介
- 灵活选择要修改的项目

## 📋 操作步骤

### 1. 准备工作

#### 账号信息
格式：`UID----GSID`，每行一个账号
```
7216903696----_2A25FiyeSDeRxGeFM6lQY8C3KwjqIHXVkATxarDV6PUJbj9AYLWvwkWpNQPbDU0aBOfxFX1cuZmYHFOOTjbztXstF
1234567890----_2A25FiyeSDeRhGeFM6lQY8C3KwjqIHXVmx1ParDV6PUJbitAYLWjCkWtNQPbDUxRt4axQRwVIyMoP0bR2mgKxTTnD
9876543210----_2A25FiyeSDeRxGeFM6lQY8C3KwjqIHXVmx1ParDV6PUJbitAYLWjCkWtNQPbDUxRt4axQRwVIyMoP0bR2mgKxTTnD
```

#### 昵称列表
每行一个昵称，程序会按顺序分配给账号：
```
小明
小红
小刚
小丽
阿强
```

#### 头像文件夹
准备一个文件夹，里面放入多张头像图片：
```
头像文件夹/
├── avatar1.jpg
├── avatar2.png
├── avatar3.jpg
├── avatar4.png
└── avatar5.jpg
```

### 2. 导入账号

1. 将账号信息复制到剪贴板
2. 点击"📋 从剪贴板"导入
3. 点击"✅ 解析账号"
4. 等待程序获取所有账号的AID和S值（状态变为"就绪"）

### 3. 配置批量修改

#### 选择修改项目
勾选要修改的项目：
- ☑️ **修改性别** - 所有账号统一设置
- ☑️ **修改昵称** - 使用昵称列表
- ☑️ **修改头像** - 随机选择头像
- ☑️ **修改简介** - 统一简介内容

#### 设置修改内容

**统一性别**：选择"男"或"女"

**昵称列表**：
```
在昵称列表文本框中输入：
小明
小红  
小刚
小丽
阿强
```
- 如果账号数量多于昵称数量，会循环使用昵称
- 例如：5个昵称，10个账号，会重复使用昵称

**头像文件夹**：
1. 点击"选择文件夹"
2. 选择包含头像图片的文件夹
3. 程序会显示找到的图片数量

**统一简介**：
```
在简介文本框中输入：
这是我的微博，欢迎关注！🎉
```

### 4. 开始批量修改

1. 点击"🚀 开始批量修改"
2. 确认修改信息：
   ```
   将对 10 个账号进行以下修改：
   • 性别 -> 男
   • 昵称
   • 头像
   • 简介
   
   确定要开始吗？
   ```
3. 点击"是"开始执行

### 5. 监控进度

- **进度条**：显示整体进度
- **状态文字**：显示当前处理的账号
- **操作日志**：显示详细的修改过程

### 6. 控制操作

- **⏸️ 暂停**：暂停当前操作，可以恢复
- **⏹️ 停止**：完全停止操作

## 🔧 高级功能

### 灵活选择修改项目
你可以只选择部分项目进行修改：

**只修改性别**：
- 只勾选"修改性别"
- 选择"男"或"女"
- 开始批量修改

**只修改昵称**：
- 只勾选"修改昵称"
- 输入昵称列表
- 开始批量修改

**组合修改**：
- 可以任意组合，比如只修改"性别+昵称"
- 或者"昵称+头像+简介"

### 昵称分配策略
- **顺序分配**：第1个账号用第1个昵称，第2个账号用第2个昵称...
- **循环使用**：如果账号多于昵称，会重复使用昵称
- **示例**：3个昵称，5个账号
  ```
  账号1 -> 昵称1
  账号2 -> 昵称2  
  账号3 -> 昵称3
  账号4 -> 昵称1 (重复)
  账号5 -> 昵称2 (重复)
  ```

### 头像随机分配
- 程序会从文件夹中随机选择头像
- 每个账号都会得到不同的头像（如果头像数量足够）
- 支持的格式：jpg, jpeg, png, gif, bmp, webp

## ⚠️ 注意事项

### 操作频率
- 程序会自动控制请求频率
- 性别/昵称/简介修改间隔2秒
- 头像修改间隔3秒
- 账号间间隔5秒

### 错误处理
- 如果某个账号修改失败，会继续处理下一个
- 最终会显示成功/失败统计
- 详细错误信息会记录在日志中

### 资源准备
- 确保头像文件夹中有足够的图片
- 昵称列表建议准备充足
- 网络连接要稳定

## 📊 批量修改示例

### 示例1：修改100个账号的性别和昵称
1. 导入100个账号
2. 准备100个昵称
3. 勾选"修改性别"和"修改昵称"
4. 设置性别为"男"
5. 输入昵称列表
6. 开始批量修改
7. 预计耗时：约25分钟（100账号 × 15秒/账号）

### 示例2：只修改头像
1. 导入50个账号
2. 准备头像文件夹（包含50+张图片）
3. 只勾选"修改头像"
4. 选择头像文件夹
5. 开始批量修改
6. 预计耗时：约12分钟（50账号 × 8秒/账号）

### 示例3：全部修改
1. 导入20个账号
2. 准备昵称列表、头像文件夹、简介内容
3. 勾选所有修改项目
4. 配置所有内容
5. 开始批量修改
6. 预计耗时：约15分钟（20账号 × 45秒/账号）

## 🎉 完成后

批量修改完成后：
- 查看操作日志了解详细结果
- 可以随机选择几个账号验证修改效果
- 如有失败的账号，可以单独重新修改

批量修改功能大大提高了效率，让你能够快速管理大量微博账号！
