#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试性别显示功能
"""

import tkinter as tk
from weibo_profile_editor import WeiboProfileEditor, AccountInfo

def test_gender_display():
    """测试不同状态下的性别显示"""
    
    # 创建测试窗口
    root = tk.Tk()
    app = WeiboProfileEditor(root)
    
    # 创建测试账号
    test_accounts = [
        # 1. 刚导入的账号（待处理状态）
        AccountInfo("**********", "test_gsid_1"),
        
        # 2. 正在获取信息的账号
        AccountInfo("**********", "test_gsid_2"),
        
        # 3. 获取成功的男性账号
        AccountInfo("**********", "test_gsid_3"),
        
        # 4. 获取成功的女性账号
        AccountInfo("**********", "test_gsid_4"),
        
        # 5. 获取成功但未设置性别的账号
        AccountInfo("**********", "test_gsid_5"),
        
        # 6. 获取失败的账号
        AccountInfo("**********", "test_gsid_6"),
    ]
    
    # 设置测试数据
    test_accounts[0].status = "待处理"
    test_accounts[0].nickname = "待处理账号"
    
    test_accounts[1].status = "获取用户信息中..."
    test_accounts[1].nickname = "获取中账号"
    
    test_accounts[2].status = "就绪"
    test_accounts[2].nickname = "男性用户"
    test_accounts[2].current_profile = {"screen_name": "男性用户", "gender": "m", "description": "这是一个男性用户"}
    
    test_accounts[3].status = "就绪"
    test_accounts[3].nickname = "女性用户"
    test_accounts[3].current_profile = {"screen_name": "女性用户", "gender": "f", "description": "这是一个女性用户"}
    
    test_accounts[4].status = "就绪"
    test_accounts[4].nickname = "未设置性别"
    test_accounts[4].current_profile = {"screen_name": "未设置性别", "description": "未设置性别的用户"}
    
    test_accounts[5].status = "用户信息获取失败"
    test_accounts[5].nickname = "失败账号"
    
    # 添加到应用
    app.accounts = test_accounts
    app.refresh_table()
    app.update_stats()
    
    app.log_message("🧪 测试数据已加载")
    app.log_message("📊 性别显示测试:")
    app.log_message("  - 待处理: 显示 '待处理'")
    app.log_message("  - 获取中: 显示 '获取中...'")
    app.log_message("  - 男性(m): 显示 '男'")
    app.log_message("  - 女性(f): 显示 '女'")
    app.log_message("  - 未设置: 显示 '未设置'")
    app.log_message("  - 失败: 显示 '获取失败'")
    app.log_message("💡 双击账号行可查看详细信息")
    
    root.mainloop()

if __name__ == "__main__":
    test_gender_display()
