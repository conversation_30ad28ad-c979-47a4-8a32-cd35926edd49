#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试性别显示问题
"""

import tkinter as tk
from weibo_profile_editor import WeiboProfileEditor, AccountInfo

def debug_gender_issue():
    """调试性别显示问题"""
    
    # 创建测试窗口
    root = tk.Tk()
    app = WeiboProfileEditor(root)
    
    # 模拟一个已获取完整信息的账号
    test_account = AccountInfo("**********", "test_gsid")
    test_account.status = "就绪"
    test_account.nickname = "测试用户"
    test_account.aid = "test_aid"
    test_account.s_value = "test_s_value"
    
    # 模拟微博API返回的用户数据
    test_account.current_profile = {
        "screen_name": "测试用户",
        "gender": "m",  # 明确设置为男性
        "description": "这是一个测试用户",
        "profile_image_url": "http://example.com/avatar.jpg"
    }
    
    # 添加到应用
    app.accounts = [test_account]
    app.refresh_table()
    app.update_stats()
    
    # 输出调试信息
    print("=== 调试信息 ===")
    print(f"账号状态: {test_account.status}")
    print(f"性别原始值: '{test_account.current_profile.get('gender', 'None')}'")
    print(f"current_profile 内容: {test_account.current_profile}")
    
    app.log_message("🔍 调试模式已启动")
    app.log_message(f"账号状态: {test_account.status}")
    app.log_message(f"性别原始值: '{test_account.current_profile.get('gender', 'None')}'")
    app.log_message("请检查表格中的性别显示是否正确")
    
    root.mainloop()

if __name__ == "__main__":
    debug_gender_issue()
