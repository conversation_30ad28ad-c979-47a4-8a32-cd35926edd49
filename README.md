# 微博国际版资料修改工具

这是一个基于你现有微博监控程序的资料修改工具，使用相同的AID和S值获取方式，支持修改微博账号的个人资料。

## 功能特点

- ✅ 支持批量导入账号
- ✅ 自动获取AID和S值
- ✅ **批量修改功能** - 一次性修改大量账号
- ✅ 修改性别 - 统一设置为男/女
- ✅ 修改昵称 - 支持昵称列表按顺序分配
- ✅ 修改个人简介 - 统一简介内容
- ✅ 修改头像 - 从文件夹随机选择头像
- ✅ 灵活选择 - 可单独修改某项或全部修改
- ✅ 进度显示 - 实时显示批量修改进度
- ✅ 暂停/停止 - 可随时暂停或停止批量操作
- ✅ 实时查看当前资料
- ✅ 详细的操作日志

## 使用方法

### 1. 准备账号信息

账号信息格式：`UID----GSID`

- **UID**: 用户ID
- **GSID**: 会话ID（程序会自动将其作为SUB Cookie使用）

示例：
```
7216903696----_2A25FiyeSDeRxGeFM6lQY8C3KwjqIHXVkATxarDV6PUJbj9AYLWvwkWpNQPbDU0aBOfxFX1cuZmYHFOOTjbztXstF
```

### 2. 导入账号

1. 将账号信息复制到剪贴板
2. 点击"📋 从剪贴板"按钮导入
3. 点击"✅ 解析账号"按钮开始解析
4. 等待程序自动获取AID和S值

### 3. 批量修改资料

#### 设置修改选项
1. 在"批量修改"区域，勾选要修改的项目：
   - ☑️ **修改性别** - 统一设置所有账号的性别
   - ☑️ **修改昵称** - 使用昵称列表按顺序分配
   - ☑️ **修改头像** - 从文件夹随机选择头像
   - ☑️ **修改简介** - 统一设置简介内容

#### 配置修改内容
1. **统一性别**: 选择"男"或"女"
2. **昵称列表**: 在文本框中输入昵称，每行一个，程序会按顺序分配给账号
3. **头像文件夹**: 点击"选择文件夹"选择包含头像图片的文件夹，程序会随机选择
4. **统一简介**: 输入要设置的统一简介内容

#### 开始批量修改
1. 点击"🚀 开始批量修改"按钮
2. 确认修改内容和账号数量
3. 程序会自动处理所有就绪的账号
4. 可以随时点击"⏸️ 暂停"或"⏹️ 停止"

#### 单个账号修改
如需修改单个账号：
1. 在"账号选择"区域选择要修改的账号
2. 点击"🔄 获取资料"按钮获取当前资料信息
3. 查看当前资料，确认要修改的内容

## 技术原理

### AID获取
程序使用与你的监控工具相同的方式获取AID：
1. 访问 `http://43.241.51.137:5454/asd/dfgh/?uid=aid` 获取登录URL
2. 访问登录URL获取AID值

### S值计算
通过尝试不同的S值组合（`abcdef0123456789` 各重复8次），找到能正常访问API的S值。

### 资料修改API
使用微博国际版的用户更新API：
- **URL**: `https://api.weibo.cn/2/users/update`
- **方法**: POST
- **参数**: 包含AID、S值、GSID等认证信息

## 注意事项

1. **GSID重要性**: GSID会被自动用作SUB Cookie，确保GSID有效且未过期
2. **请求频率**: 程序会自动控制请求频率，避免触发限制
3. **错误处理**: 如果修改失败，请检查账号状态和网络连接
4. **安全性**: 请妥善保管账号信息，不要泄露给他人

## 界面说明

- **账号导入区域**: 输入和解析账号信息
- **账号选择区域**: 选择要操作的账号（单个修改时使用）
- **批量修改区域**:
  - 修改选项复选框：选择要修改的项目
  - 统一性别设置：选择男/女
  - 昵称列表：输入昵称列表，每行一个
  - 头像文件夹：选择包含头像的文件夹
  - 统一简介：输入统一的简介内容
  - 批量操作按钮：开始/暂停/停止
  - 进度显示：显示当前处理进度
- **当前选中账号资料**: 显示选中账号的详细资料信息
- **操作日志**: 显示所有操作的详细日志

## 常见问题

### Q: 提示"账号信息未准备就绪"
A: 请等待程序完成AID和S值的获取，状态显示为"就绪"后再操作。

### Q: 修改失败提示权限错误
A: 请检查GSID是否正确且未过期，GSID会被用作SUB Cookie进行身份验证。

### Q: 程序无响应
A: 可能是网络问题，请检查网络连接或稍后重试。

## 运行环境

- Python 3.6+
- tkinter (通常随Python安装)
- requests

安装依赖：
```bash
pip install requests
```

运行程序：
```bash
python weibo_profile_editor.py
```
