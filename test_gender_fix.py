#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试性别显示修复
"""

import tkinter as tk
from weibo_profile_editor import WeiboProfileEditor, AccountInfo
import time

def test_gender_fix():
    """测试性别显示修复"""
    
    # 创建测试窗口
    root = tk.Tk()
    app = WeiboProfileEditor(root)
    
    # 创建多个测试账号，模拟不同的性别情况
    test_accounts = [
        # 男性账号
        AccountInfo("**********", "gsid_1"),
        # 女性账号  
        AccountInfo("**********", "gsid_2"),
        # 未设置性别的账号
        AccountInfo("**********", "gsid_3"),
        # 异常性别值的账号
        AccountInfo("**********", "gsid_4"),
    ]
    
    # 设置第一个账号（男性）
    test_accounts[0].status = "就绪"
    test_accounts[0].nickname = "男性用户"
    test_accounts[0].aid = "aid_1"
    test_accounts[0].s_value = "s_1"
    test_accounts[0].current_profile = {
        "screen_name": "男性用户",
        "gender": "m",
        "description": "我是男性用户"
    }
    
    # 设置第二个账号（女性）
    test_accounts[1].status = "就绪"
    test_accounts[1].nickname = "女性用户"
    test_accounts[1].aid = "aid_2"
    test_accounts[1].s_value = "s_2"
    test_accounts[1].current_profile = {
        "screen_name": "女性用户",
        "gender": "f",
        "description": "我是女性用户"
    }
    
    # 设置第三个账号（未设置性别）
    test_accounts[2].status = "就绪"
    test_accounts[2].nickname = "未设置性别"
    test_accounts[2].aid = "aid_3"
    test_accounts[2].s_value = "s_3"
    test_accounts[2].current_profile = {
        "screen_name": "未设置性别",
        "description": "我没有设置性别"
        # 注意：没有 gender 字段
    }
    
    # 设置第四个账号（异常性别值）
    test_accounts[3].status = "就绪"
    test_accounts[3].nickname = "异常性别值"
    test_accounts[3].aid = "aid_4"
    test_accounts[3].s_value = "s_4"
    test_accounts[3].current_profile = {
        "screen_name": "异常性别值",
        "gender": "unknown",  # 异常值
        "description": "我有异常的性别值"
    }
    
    # 添加到应用
    app.accounts = test_accounts
    app.refresh_table()
    app.update_stats()
    
    # 输出调试信息
    print("=== 性别显示测试 ===")
    for i, account in enumerate(test_accounts):
        gender_raw = account.current_profile.get('gender', 'None')
        print(f"账号 {i+1}: 状态={account.status}, 原始性别='{gender_raw}'")
    
    app.log_message("🧪 性别显示测试已启动")
    app.log_message("预期结果:")
    app.log_message("  账号1: 应显示 '男'")
    app.log_message("  账号2: 应显示 '女'")
    app.log_message("  账号3: 应显示 '未设置'")
    app.log_message("  账号4: 应显示 '未设置'")
    app.log_message("💡 如果显示不正确，请点击'刷新表格'按钮")
    
    root.mainloop()

if __name__ == "__main__":
    test_gender_fix()
