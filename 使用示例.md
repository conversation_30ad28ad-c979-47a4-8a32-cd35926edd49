# 微博资料修改工具使用示例

## 快速开始

### 1. 运行程序
```bash
python weibo_profile_editor.py
```

或者先测试：
```bash
python test_startup.py
```

### 2. 导入账号信息

在"账号导入"区域的文本框中输入账号信息，格式为：
```
UID----GSID
```

示例：
```
7216903696----_2A25FiyeSDeRxGeFM6lQY8C3KwjqIHXVkATxarDV6PUJbj9AYLWvwkWpNQPbDU0aBOfxFX1cuZmYHFOOTjbztXstF
1234567890----_2A25FiyeSDeRhGeFM6lQY8C3KwjqIHXVkATxarDV6PUJbitAYLWjCkWtNQPbDUxRt4axQRwVIyMoP0bR2mgKxTTnD
```

### 3. 解析账号
1. 点击"📋 从剪贴板"按钮（如果账号信息在剪贴板中）
2. 点击"✅ 解析账号"按钮
3. 等待程序自动获取AID和S值（这可能需要几分钟）

### 4. 选择账号
在"账号选择"下拉框中选择要修改的账号，然后点击"🔄 获取资料"按钮获取当前资料。

### 5. 修改资料

#### 修改性别
1. 选择"男"或"女"
2. 点击"修改性别"按钮

#### 修改昵称
1. 在昵称输入框中输入新昵称
2. 点击"修改昵称"按钮

#### 修改简介
1. 在简介文本框中输入新的个人简介
2. 点击"修改简介"按钮

#### 修改头像
1. 点击"选择图片"按钮选择头像文件
2. 支持的格式：jpg, jpeg, png, gif, bmp, webp
3. 点击"修改头像"按钮上传新头像

## 界面说明

### 账号导入区域
- **文本框**: 输入账号信息
- **📋 从剪贴板**: 从剪贴板导入账号信息
- **✅ 解析账号**: 开始解析账号并获取AID/S值
- **🗑️ 清空**: 清空输入框

### 账号选择区域
- **下拉框**: 选择要操作的账号
- **🔄 获取资料**: 获取选中账号的当前资料信息

### 资料修改区域
- **性别**: 单选按钮选择性别
- **昵称**: 文本输入框
- **简介**: 多行文本框
- **头像**: 文件选择器

### 当前资料显示
显示账号的详细资料信息，包括：
- 昵称
- 性别
- 简介
- 头像URL
- 粉丝数、关注数、微博数
- 注册时间、认证信息、地区等

### 操作日志
显示所有操作的详细日志，包括：
- 账号解析过程
- AID和S值获取状态
- 资料修改结果
- 错误信息等

## 注意事项

1. **GSID有效性**: 确保GSID是有效的且未过期
2. **网络连接**: 需要稳定的网络连接
3. **图片大小**: 头像图片建议不超过5MB
4. **操作频率**: 不要过于频繁地修改资料，避免触发限制
5. **账号状态**: 只有状态为"就绪"的账号才能进行修改操作

## 常见问题

### Q: AID获取失败
A: 可能是网络问题或API服务不可用，请稍后重试。

### Q: S值计算失败
A: 可能是GSID无效或过期，请检查GSID是否正确。

### Q: 修改操作失败
A: 检查账号状态是否为"就绪"，以及网络连接是否正常。

### Q: 头像上传失败
A: 检查图片格式是否支持，文件大小是否合适。

## 技术细节

程序使用与你的微博监控工具相同的技术：
- **AID获取**: 通过特定API获取设备标识
- **S值计算**: 尝试不同组合找到有效的签名值
- **API调用**: 使用微博国际版的官方API接口
- **身份验证**: 使用GSID作为SUB Cookie进行身份验证

## 安全提醒

- 不要将账号信息分享给他人
- 定期检查账号安全状态
- 如发现异常活动，及时修改密码
