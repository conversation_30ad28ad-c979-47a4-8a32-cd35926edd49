#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
微博国际版资料修改工具
支持修改性别、昵称、简介等个人资料
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext, filedialog
from tkinter.ttk import Treeview
import threading
import time
import requests
import json
from typing import List


class AccountInfo:
    """账号信息类"""
    def __init__(self, uid: str, gsid: str):
        self.uid = uid
        self.gsid = gsid
        self.aid = ""
        self.s_value = ""
        self.nickname = ""
        self.status = "待处理"
        self.current_profile = {}  # 存储当前资料信息


class WeiboProfileEditor:
    def __init__(self, root):
        self.root = root
        self.root.title("微博资料批量修改工具")
        self.root.geometry("1100x700")
        self.root.resizable(True, True)

        # 设置样式
        style = ttk.Style()
        try:
            style.theme_use('clam')
        except:
            pass
        
        # 账号列表
        self.accounts: List[AccountInfo] = []
        self.selected_accounts = []  # 选中的账号

        # 头像路径
        self.selected_avatar_folder = None

        # 批量修改控制
        self.batch_running = False
        self.batch_paused = False
        self.batch_thread = None

        # 账号输入组件（在对话框中创建）
        self.account_input = None

        self.create_widgets()

        # 显示欢迎信息
        self.show_welcome_message()
        
    def create_widgets(self):
        """创建界面组件 - 表格版"""
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(1, weight=1)  # 表格区域权重最大
        
        # 顶部工具栏
        toolbar_frame = ttk.Frame(main_frame)
        toolbar_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        toolbar_frame.columnconfigure(1, weight=1)

        # 左侧导入区域
        import_frame = ttk.Frame(toolbar_frame)
        import_frame.grid(row=0, column=0, sticky=tk.W)

        ttk.Label(import_frame, text="账号导入:").pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(import_frame, text="从剪贴板", command=self.import_from_clipboard).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(import_frame, text="解析账号", command=self.parse_accounts).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(import_frame, text="清空表格", command=self.clear_accounts).pack(side=tk.LEFT, padx=(0, 10))

        # 右侧操作区域
        action_frame = ttk.Frame(toolbar_frame)
        action_frame.grid(row=0, column=1, sticky=tk.E)

        ttk.Button(action_frame, text="全选", command=self.select_all).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(action_frame, text="反选", command=self.select_none).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(action_frame, text="刷新表格", command=self.manual_refresh).pack(side=tk.LEFT, padx=(0, 5))
        ttk.Button(action_frame, text="修改选中", command=self.modify_selected).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(action_frame, text="导出结果", command=self.export_results).pack(side=tk.LEFT)
        
        # 账号表格区域
        table_frame = ttk.LabelFrame(main_frame, text="账号列表", padding="5")
        table_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        table_frame.columnconfigure(0, weight=1)
        table_frame.rowconfigure(0, weight=1)

        # 创建表格
        columns = ('选择', '序号', 'UID', '昵称', '性别', '个性签名', '状态')
        self.account_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=12)

        # 设置列标题和宽度
        self.account_tree.heading('选择', text='选择')
        self.account_tree.heading('序号', text='序号')
        self.account_tree.heading('UID', text='UID')
        self.account_tree.heading('昵称', text='昵称')
        self.account_tree.heading('性别', text='性别')
        self.account_tree.heading('个性签名', text='个性签名')
        self.account_tree.heading('状态', text='状态')

        # 设置列宽
        self.account_tree.column('选择', width=50, anchor='center')
        self.account_tree.column('序号', width=50, anchor='center')
        self.account_tree.column('UID', width=100, anchor='center')
        self.account_tree.column('昵称', width=120, anchor='center')
        self.account_tree.column('性别', width=60, anchor='center')
        self.account_tree.column('个性签名', width=200, anchor='w')
        self.account_tree.column('状态', width=100, anchor='center')

        # 添加滚动条
        scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.account_tree.yview)
        self.account_tree.configure(yscrollcommand=scrollbar.set)

        # 布局表格和滚动条
        self.account_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))

        # 绑定事件
        self.account_tree.bind('<Button-1>', self.on_tree_click)
        self.account_tree.bind('<Double-1>', self.on_tree_double_click)
        
        # 修改配置区域
        config_frame = ttk.LabelFrame(main_frame, text="修改配置", padding="10")
        config_frame.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        config_frame.columnconfigure(1, weight=1)

        # 第一行：修改选项
        options_frame = ttk.Frame(config_frame)
        options_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))

        ttk.Label(options_frame, text="修改项目:").pack(side=tk.LEFT, padx=(0, 10))

        self.modify_gender_var = tk.BooleanVar(value=True)
        self.modify_nickname_var = tk.BooleanVar(value=True)
        self.modify_avatar_var = tk.BooleanVar(value=True)
        self.modify_description_var = tk.BooleanVar(value=False)

        ttk.Checkbutton(options_frame, text="性别", variable=self.modify_gender_var).pack(side=tk.LEFT, padx=(0, 15))
        ttk.Checkbutton(options_frame, text="昵称", variable=self.modify_nickname_var).pack(side=tk.LEFT, padx=(0, 15))
        ttk.Checkbutton(options_frame, text="头像", variable=self.modify_avatar_var).pack(side=tk.LEFT, padx=(0, 15))
        ttk.Checkbutton(options_frame, text="简介", variable=self.modify_description_var).pack(side=tk.LEFT)

        # 第二行：配置详情
        detail_frame = ttk.Frame(config_frame)
        detail_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        detail_frame.columnconfigure(1, weight=1)
        detail_frame.columnconfigure(3, weight=1)

        # 性别设置
        ttk.Label(detail_frame, text="性别:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        self.batch_gender_var = tk.StringVar(value="m")
        gender_frame = tk.Frame(detail_frame)
        gender_frame.grid(row=0, column=1, sticky=tk.W, padx=(0, 20))
        ttk.Radiobutton(gender_frame, text="男", variable=self.batch_gender_var, value="m").pack(side=tk.LEFT, padx=(0, 10))
        ttk.Radiobutton(gender_frame, text="女", variable=self.batch_gender_var, value="f").pack(side=tk.LEFT)

        # 头像文件夹
        ttk.Label(detail_frame, text="头像:").grid(row=0, column=2, sticky=tk.W, padx=(0, 5))
        avatar_frame = tk.Frame(detail_frame)
        avatar_frame.grid(row=0, column=3, sticky=(tk.W, tk.E))
        self.avatar_folder_var = tk.StringVar(value="未选择")
        ttk.Label(avatar_frame, textvariable=self.avatar_folder_var, foreground="gray").pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(avatar_frame, text="选择文件夹", command=self.select_avatar_folder).pack(side=tk.LEFT)

        # 第三行：昵称和简介
        text_frame = ttk.Frame(config_frame)
        text_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        text_frame.columnconfigure(1, weight=1)
        text_frame.columnconfigure(3, weight=1)

        # 昵称列表
        ttk.Label(text_frame, text="昵称:").grid(row=0, column=0, sticky=(tk.W, tk.N), padx=(0, 5))
        self.nickname_text = scrolledtext.ScrolledText(text_frame, height=3, width=25)
        self.nickname_text.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 20))

        # 简介
        ttk.Label(text_frame, text="简介:").grid(row=0, column=2, sticky=(tk.W, tk.N), padx=(0, 5))
        self.batch_description_text = tk.Text(text_frame, height=3, width=25)
        self.batch_description_text.grid(row=0, column=3, sticky=(tk.W, tk.E))

        # 第四行：操作按钮
        button_frame = ttk.Frame(config_frame)
        button_frame.grid(row=3, column=0, columnspan=2, pady=(10, 0))

        ttk.Button(button_frame, text="开始修改", command=self.start_batch_update).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="暂停", command=self.pause_batch_update).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="停止", command=self.stop_batch_update).pack(side=tk.LEFT, padx=(0, 20))

        # 进度显示
        self.progress_var = tk.StringVar(value="等待开始...")
        ttk.Label(button_frame, textvariable=self.progress_var).pack(side=tk.LEFT, padx=(0, 10))

        self.progress_bar = ttk.Progressbar(button_frame, mode='determinate', length=200)
        self.progress_bar.pack(side=tk.LEFT)
        
        # 日志区域
        log_frame = ttk.LabelFrame(main_frame, text="操作日志", padding="5")
        log_frame.grid(row=3, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        main_frame.rowconfigure(3, weight=1)  # 日志区域也有一定权重

        # 日志工具栏
        log_toolbar = ttk.Frame(log_frame)
        log_toolbar.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 5))

        ttk.Button(log_toolbar, text="清空日志", command=self.clear_log).pack(side=tk.RIGHT)

        # 创建日志文本框
        self.log_text = scrolledtext.ScrolledText(log_frame, height=8, width=80, state=tk.DISABLED)
        self.log_text.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        log_frame.rowconfigure(1, weight=1)

        # 状态栏
        status_frame = ttk.Frame(main_frame)
        status_frame.grid(row=4, column=0, sticky=(tk.W, tk.E), pady=(10, 0))
        status_frame.columnconfigure(0, weight=1)

        self.status_var = tk.StringVar(value="就绪")
        ttk.Label(status_frame, textvariable=self.status_var).grid(row=0, column=0, sticky=tk.W)

        # 统计信息
        self.stats_var = tk.StringVar(value="账号: 0 | 选中: 0 | 就绪: 0")
        ttk.Label(status_frame, textvariable=self.stats_var).grid(row=0, column=1, sticky=tk.E)
    
    def update_status(self, message):
        """更新状态栏"""
        timestamp = time.strftime("%H:%M:%S")
        self.status_var.set(f"[{timestamp}] {message}")
        self.root.update_idletasks()

    def log_message(self, message):
        """添加日志消息"""
        timestamp = time.strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"

        # 在主线程中更新UI
        def update_log():
            self.log_text.config(state=tk.NORMAL)
            self.log_text.insert(tk.END, log_entry)
            self.log_text.see(tk.END)
            self.log_text.config(state=tk.DISABLED)

        if threading.current_thread() == threading.main_thread():
            update_log()
        else:
            self.root.after(0, update_log)

    def clear_log(self):
        """清空日志"""
        self.log_text.config(state=tk.NORMAL)
        self.log_text.delete(1.0, tk.END)
        self.log_text.config(state=tk.DISABLED)
        self.log_message("📝 日志已清空")

    def show_welcome_message(self):
        """显示欢迎信息"""
        self.log_message("🎉 欢迎使用微博资料批量修改工具！")
        self.log_message("📋 使用步骤：")
        self.log_message("   1. 点击'从剪贴板'导入账号信息（格式：UID----GSID）")
        self.log_message("   2. 等待程序自动获取账号详细信息")
        self.log_message("   3. 选择要修改的账号和修改项目")
        self.log_message("   4. 点击'开始修改'进行批量修改")
        self.log_message("💡 提示：可以随时暂停或停止批量操作")

    def update_stats(self):
        """更新统计信息"""
        total_accounts = len(self.accounts)
        selected_accounts = len(self.selected_accounts)
        ready_accounts = len([acc for acc in self.accounts if acc.status == "就绪"])

        self.stats_var.set(f"账号: {total_accounts} | 选中: {selected_accounts} | 就绪: {ready_accounts}")

    def on_tree_click(self, event):
        """表格单击事件 - 切换选择状态"""
        item = self.account_tree.identify('item', event.x, event.y)
        if item:
            # 获取点击的列
            column = self.account_tree.identify('column', event.x, event.y)
            if column == '#1':  # 选择列
                self.toggle_selection(item)

    def on_tree_double_click(self, event):
        """表格双击事件 - 查看头像"""
        item = self.account_tree.selection()[0] if self.account_tree.selection() else None
        if item:
            self.show_avatar(item)

    def toggle_selection(self, item):
        """切换选择状态"""
        values = list(self.account_tree.item(item, 'values'))
        if values[0] == '☑':
            values[0] = '☐'
            # 从选中列表移除
            uid = values[2]
            self.selected_accounts = [acc for acc in self.selected_accounts if acc.uid != uid]
        else:
            values[0] = '☑'
            # 添加到选中列表
            uid = values[2]
            account = next((acc for acc in self.accounts if acc.uid == uid), None)
            if account and account not in self.selected_accounts:
                self.selected_accounts.append(account)

        self.account_tree.item(item, values=values)
        self.update_status(f"已选择 {len(self.selected_accounts)} 个账号")
        self.update_stats()

    def select_all(self):
        """全选"""
        self.selected_accounts = self.accounts.copy()
        for item in self.account_tree.get_children():
            values = list(self.account_tree.item(item, 'values'))
            values[0] = '☑'
            self.account_tree.item(item, values=values)
        self.update_status(f"已全选 {len(self.selected_accounts)} 个账号")
        self.update_stats()

    def select_none(self):
        """反选"""
        self.selected_accounts = []
        for item in self.account_tree.get_children():
            values = list(self.account_tree.item(item, 'values'))
            values[0] = '☐'
            self.account_tree.item(item, values=values)
        self.update_status("已取消所有选择")
        self.update_stats()

    def export_results(self):
        """导出账号信息到文件"""
        if not self.accounts:
            messagebox.showwarning("警告", "没有账号信息可导出")
            return

        try:
            from tkinter import filedialog
            import csv
            import os

            # 选择保存文件
            file_path = filedialog.asksaveasfilename(
                title="导出账号信息",
                defaultextension=".csv",
                filetypes=[("CSV文件", "*.csv"), ("文本文件", "*.txt"), ("所有文件", "*.*")]
            )

            if not file_path:
                return

            # 准备导出数据
            export_data = []
            for i, account in enumerate(self.accounts):
                # 根据账号状态显示性别信息
                if account.status == "就绪":
                    gender = '男' if account.current_profile.get('gender') == 'm' else '女' if account.current_profile.get('gender') == 'f' else '未设置'
                elif account.status in ["获取AID中...", "计算S值中...", "获取用户信息中..."]:
                    gender = '获取中'
                elif "失败" in account.status or "异常" in account.status:
                    gender = '获取失败'
                else:
                    gender = '待处理'

                description = account.current_profile.get('description', '无') or '无'

                export_data.append({
                    '序号': i + 1,
                    'UID': account.uid,
                    '昵称': account.nickname or '未获取',
                    '性别': gender,
                    '个性签名': description,
                    '状态': account.status,
                    'GSID': account.gsid,
                    'AID': account.aid,
                    'S值': account.s_value
                })

            # 根据文件扩展名选择导出格式
            if file_path.lower().endswith('.csv'):
                # CSV格式
                with open(file_path, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    fieldnames = ['序号', 'UID', '昵称', '性别', '个性签名', '状态', 'GSID', 'AID', 'S值']
                    writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                    writer.writeheader()
                    writer.writerows(export_data)
            else:
                # 文本格式
                with open(file_path, 'w', encoding='utf-8') as txtfile:
                    txtfile.write("微博账号信息导出\n")
                    txtfile.write("=" * 50 + "\n\n")

                    for data in export_data:
                        txtfile.write(f"序号: {data['序号']}\n")
                        txtfile.write(f"UID: {data['UID']}\n")
                        txtfile.write(f"昵称: {data['昵称']}\n")
                        txtfile.write(f"性别: {data['性别']}\n")
                        txtfile.write(f"个性签名: {data['个性签名']}\n")
                        txtfile.write(f"状态: {data['状态']}\n")
                        txtfile.write(f"GSID: {data['GSID']}\n")
                        txtfile.write(f"AID: {data['AID']}\n")
                        txtfile.write(f"S值: {data['S值']}\n")
                        txtfile.write("-" * 30 + "\n\n")

            self.log_message(f"✅ 账号信息已导出到: {os.path.basename(file_path)}")
            messagebox.showinfo("导出成功", f"账号信息已成功导出到:\n{file_path}")

        except Exception as e:
            self.log_message(f"❌ 导出失败: {e}")
            messagebox.showerror("导出失败", f"导出过程中发生错误:\n{e}")

    def show_avatar(self, item):
        """显示账号详细信息"""
        values = self.account_tree.item(item, 'values')
        uid = values[2]
        account = next((acc for acc in self.accounts if acc.uid == uid), None)

        if account:
            # 构建详细信息
            info_lines = [
                f"账号: {account.nickname or '未获取'}",
                f"UID: {uid}",
                f"状态: {account.status}",
                f"AID: {account.aid or '未获取'}",
                f"S值: {account.s_value or '未计算'}",
                ""
            ]

            if account.current_profile:
                gender_raw = account.current_profile.get('gender', '未设置')
                gender_display = '男' if gender_raw == 'm' else '女' if gender_raw == 'f' else f'原始值: {gender_raw}'

                info_lines.extend([
                    "用户资料:",
                    f"  性别: {gender_display}",
                    f"  简介: {account.current_profile.get('description', '无') or '无'}",
                    f"  头像: {'有' if account.current_profile.get('profile_image_url') else '无'}"
                ])

                if account.current_profile.get('profile_image_url'):
                    info_lines.append(f"  头像URL: {account.current_profile['profile_image_url']}")
            else:
                info_lines.append("用户资料: 未获取")

            messagebox.showinfo("账号详细信息", "\n".join(info_lines))
        else:
            messagebox.showinfo("账号信息", f"未找到UID为 {uid} 的账号")

    def modify_selected(self):
        """修改选中的账号"""
        if not self.selected_accounts:
            messagebox.showwarning("警告", "请先选择要修改的账号")
            return
        self.start_batch_update()

    def refresh_table(self):
        """刷新表格显示"""
        # 清空表格
        for item in self.account_tree.get_children():
            self.account_tree.delete(item)

        # 重新添加数据
        for i, account in enumerate(self.accounts):
            is_selected = '☑' if account in self.selected_accounts else '☐'
            nickname = account.nickname or '获取中...'

            # 根据账号状态显示性别信息
            if account.status == "就绪":
                gender_raw = account.current_profile.get('gender', '')
                if gender_raw == 'm':
                    gender = '男'
                elif gender_raw == 'f':
                    gender = '女'
                else:
                    gender = '未设置'
                    # 调试信息：记录原始性别值
                    if gender_raw:
                        print(f"DEBUG: 账号 {account.uid} 原始性别值: '{gender_raw}'")
            elif account.status in ["获取AID中...", "计算S值中...", "获取用户信息中..."]:
                gender = '获取中...'
            elif "失败" in account.status or "异常" in account.status:
                gender = '获取失败'
            else:
                gender = '待处理'

            description = account.current_profile.get('description', '无') or '无'
            if len(description) > 30:
                description = description[:30] + '...'

            self.account_tree.insert('', 'end', values=(
                is_selected,
                i + 1,
                account.uid,
                nickname,
                gender,
                description,
                account.status
            ))

        # 更新统计信息
        self.update_stats()
    
    def import_from_clipboard(self):
        """从剪贴板导入账号"""
        try:
            clipboard_text = self.root.clipboard_get()
            if clipboard_text:
                # 显示导入对话框
                self.show_import_dialog(clipboard_text.strip())
            else:
                messagebox.showwarning("警告", "剪贴板为空")
        except Exception as e:
            messagebox.showerror("错误", f"读取剪贴板失败: {e}")

    def show_import_dialog(self, initial_text=""):
        """显示账号导入对话框"""
        dialog = tk.Toplevel(self.root)
        dialog.title("导入账号信息")
        dialog.geometry("600x400")
        dialog.transient(self.root)
        dialog.grab_set()

        # 居中显示
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (dialog.winfo_width() // 2)
        y = (dialog.winfo_screenheight() // 2) - (dialog.winfo_height() // 2)
        dialog.geometry(f"+{x}+{y}")

        # 主框架
        main_frame = ttk.Frame(dialog, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 说明标签
        ttk.Label(main_frame, text="请输入账号信息，格式：UID----GSID，每行一个账号").pack(anchor=tk.W, pady=(0, 10))

        # 文本输入区域
        text_frame = ttk.Frame(main_frame)
        text_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        self.account_input = scrolledtext.ScrolledText(text_frame, height=15, width=70)
        self.account_input.pack(fill=tk.BOTH, expand=True)

        if initial_text:
            self.account_input.insert(1.0, initial_text)

        # 按钮区域
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X)

        ttk.Button(button_frame, text="解析账号", command=lambda: self.parse_accounts_from_dialog(dialog)).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="取消", command=dialog.destroy).pack(side=tk.LEFT)

    def parse_accounts_from_dialog(self, dialog):
        """从对话框解析账号信息"""
        account_text = self.account_input.get(1.0, tk.END).strip()

        if not account_text:
            messagebox.showerror("错误", "请输入账号信息")
            return

        lines = [line.strip() for line in account_text.split('\n') if line.strip()]

        if not lines:
            messagebox.showerror("错误", "没有有效的账号信息")
            return

        # 清空现有账号列表
        self.accounts.clear()
        self.selected_accounts.clear()

        # 解析每行账号
        valid_accounts = 0
        for i, line in enumerate(lines, 1):
            parts = line.split('----')
            if len(parts) < 2:
                self.log_message(f"❌ 第{i}行格式错误，需要UID和GSID: {line[:50]}...")
                continue

            try:
                uid = parts[0].strip()
                gsid = parts[1].strip()

                if not uid or not gsid:
                    self.log_message(f"❌ 第{i}行UID或GSID为空，跳过")
                    continue

                account = AccountInfo(uid, gsid)
                self.accounts.append(account)
                valid_accounts += 1

            except Exception as e:
                self.log_message(f"❌ 第{i}行解析失败: {e}")
                continue

        if valid_accounts > 0:
            self.update_status(f"成功解析 {valid_accounts} 个账号")
            self.refresh_table()
            self.update_stats()
            dialog.destroy()
            # 开始获取账号详细信息
            threading.Thread(target=self.fetch_account_details, daemon=True).start()
        else:
            messagebox.showerror("错误", "没有成功解析任何账号")

    def parse_accounts(self):
        """解析账号信息 - 兼容性方法"""
        self.show_import_dialog()
    
    def clear_accounts(self):
        """清空所有账号"""
        if self.accounts:
            result = messagebox.askyesno("确认", "确定要清空所有账号吗？")
            if result:
                self.accounts.clear()
                self.selected_accounts.clear()
                self.refresh_table()
                self.update_stats()
                self.update_status("已清空所有账号")

    def fetch_account_details(self):
        """获取账号详细信息（AID、S值、昵称）"""
        self.update_status("开始获取账号详细信息...")

        for i, account in enumerate(self.accounts):
            try:
                self.update_status(f"处理账号 {i+1}/{len(self.accounts)}: {account.uid}")

                # 获取AID
                account.status = "获取AID中..."
                self.root.after(0, self.refresh_table)
                if not self.get_account_aid(account):
                    account.status = "AID获取失败"
                    self.root.after(0, self.refresh_table)
                    continue

                # 计算S值
                account.status = "计算S值中..."
                self.root.after(0, self.refresh_table)
                if not self.calculate_account_s_value(account):
                    account.status = "S值计算失败"
                    self.root.after(0, self.refresh_table)
                    continue

                # 获取用户信息
                account.status = "获取用户信息中..."
                self.root.after(0, self.refresh_table)
                if not self.get_account_info(account):
                    account.status = "用户信息获取失败"
                    self.root.after(0, self.refresh_table)
                    continue

                account.status = "就绪"
                self.root.after(0, self.refresh_table)
                self.update_status(f"账号 {account.uid} ({account.nickname}) 信息获取完成")

                # 间隔1秒避免请求过快
                time.sleep(1)

            except Exception as e:
                self.update_status(f"账号 {account.uid} 处理失败: {e}")
                account.status = f"错误: {str(e)[:20]}"
                self.root.after(0, self.refresh_table)

        self.update_status("所有账号信息获取完成！")

    def get_account_aid(self, account: AccountInfo) -> bool:
        """获取账号AID"""
        try:
            # 第一步：获取登录URL
            url1 = "http://43.241.51.137:5454/asd/dfgh/?uid=aid"
            response1 = requests.get(url1, timeout=10)

            if response1.status_code != 200:
                return False

            data1 = response1.json()
            if data1.get('code') != 200 or 'result' not in data1 or 'url' not in data1['result']:
                return False

            # 第二步：访问登录URL获取AID
            login_url = data1['result']['url']
            response2 = requests.get(login_url, timeout=10)

            if response2.status_code != 200:
                return False

            data2 = response2.json()
            if 'aid' not in data2:
                return False

            account.aid = data2['aid']
            return True

        except Exception:
            return False

    def calculate_account_s_value(self, account: AccountInfo) -> bool:
        """计算账号S值"""
        try:
            url = "https://api.weibo.cn/2/users/show"
            headers = {
                "User-Agent": "WeiboOverseas/6.7.9 (iPhone; iOS 18.6.1; Scale/2.00)",
                "Referer": "https://api.weibo.cn/2/users/show"
            }

            # 尝试不同的S值
            s_values = [c*8 for c in "abcdef0123456789"]

            for s in s_values:
                params = {
                    "uid": account.uid,
                    "gsid": account.gsid,
                    "s": s,
                    "c": "weicoabroad",
                    "from": "12DC193010",
                    "ua": "iPhone13,4_iOS18.5_Weibo_intl._6790_wifi__iphone__os18.5",
                    "v_p": "59",
                    "lang": "zh_CN",
                    "aid": account.aid
                }

                try:
                    resp = requests.get(url, headers=headers, params=params, timeout=8)
                    text = resp.text

                    # 只要不是客户端身份校验失败，就判定为S值正确
                    if resp.status_code == 200 and ('客户端身份校验失败' not in text and '"errno":-105' not in text):
                        account.s_value = s
                        return True

                except Exception:
                    continue

            return False

        except Exception:
            return False

    def get_account_info(self, account: AccountInfo) -> bool:
        """获取账号基本信息"""
        try:
            url = "https://api.weibo.cn/2/users/show"
            headers = {
                "User-Agent": "WeiboOverseas/6.7.9 (iPhone; iOS 18.6.1; Scale/2.00)",
                "Cookie": f"SUB={account.gsid}"
            }

            params = {
                "uid": account.uid,
                "gsid": account.gsid,
                "s": account.s_value,
                "c": "weicoabroad",
                "from": "12DC193010",
                "ua": "iPhone13,4_iOS18.5_Weibo_intl._6790_wifi__iphone__os18.5",
                "v_p": "59",
                "lang": "zh_CN",
                "aid": account.aid
            }

            response = requests.get(url, headers=headers, params=params, timeout=10)

            if response.status_code == 200:
                try:
                    data = response.json()

                    if 'screen_name' in data:
                        account.nickname = data['screen_name']
                        account.current_profile = data

                        # 记录性别信息
                        gender_info = data.get('gender', '未设置')
                        gender_display = '男' if gender_info == 'm' else '女' if gender_info == 'f' else f'原始值: {gender_info}'

                        self.log_message(f"✅ 用户信息获取成功: {account.nickname} (性别: {gender_display})")

                        # 强制刷新表格显示
                        self.root.after(100, self.refresh_table)  # 延迟100ms确保数据已更新
                        return True
                    elif 'errmsg' in data:
                        error_msg = data.get('errmsg', '未知错误')
                        self.log_message(f"❌ 账号异常: {error_msg}")
                        account.status = "异常账号"
                        account.nickname = "异常账号"
                        return False
                    else:
                        self.log_message(f"❌ 用户信息响应格式异常")
                        return False

                except json.JSONDecodeError:
                    self.log_message(f"❌ 用户信息解析失败")
                    return False
            else:
                self.log_message(f"❌ 用户信息请求失败: HTTP {response.status_code}")
                return False

        except Exception as e:
            self.log_message(f"❌ 获取用户信息异常: {e}")
            return False

    def get_current_profile(self):
        """获取当前选中账号的资料 - 简化版"""
        if not self.accounts or self.current_account_index >= len(self.accounts):
            messagebox.showwarning("警告", "请先选择账号")
            return

        account = self.accounts[self.current_account_index]
        if account.status != "就绪":
            messagebox.showwarning("警告", "账号信息未准备就绪，请等待")
            return

        self.log_message(f"� 当前账号: {account.uid} ({account.nickname}) - 状态: {account.status}")

    def _update_profile_display(self, profile: dict):
        """更新资料显示 - 刷新表格"""
        self.refresh_table()





    def _update_gender(self, account: AccountInfo, gender: str):
        """修改性别的线程函数"""
        try:
            self.log_message(f"🔄 正在修改账号 {account.uid} 的性别为: {'男' if gender == 'm' else '女'}")

            url = "https://api.weibo.cn/2/users/update"
            headers = {
                "Host": "api.weibo.cn",
                "X-Sessionid": "FF428FE1-F3B7-4816-ABEB-6793B384E835",
                "Content-Type": "application/x-www-form-urlencoded",
                "Accept": "*/*",
                "Accept-Encoding": "gzip, deflate, br",
                "User-Agent": "WeiboOverseas/6.7.9 (iPhone; iOS 18.5; Scale/3.00)",
                "Accept-Language": "zh-Hans-GB;q=1, zh-Hant-GB;q=0.9, yue-Hant-GB;q=0.8"
            }

            # 设置Cookie（SUB使用GSID）
            cookies = {
                "SUB": account.gsid
            }

            # 构建请求数据
            data = {
                "aid": account.aid,
                "c": "weicoabroad",
                "from": "12DC193010",
                "gender": gender,
                "gsid": account.gsid,
                "lang": "zh_CN",
                "s": account.s_value,
                "ua": "iPhone13,4_iOS18.5_Weibo_intl._6790_wifi__iphone__os18.5",
                "v_p": "59"
            }

            response = requests.post(url, headers=headers, cookies=cookies, data=data, timeout=15)

            if response.status_code == 200:
                try:
                    result = response.json()
                    if 'screen_name' in result:
                        self.log_message(f"✅ 性别修改成功: {result.get('screen_name')} -> {'男' if gender == 'm' else '女'}")
                        # 更新本地资料信息
                        account.current_profile['gender'] = gender
                        # 刷新显示
                        self.root.after(0, self._update_profile_display, account.current_profile)
                        return True
                    else:
                        error_msg = result.get('errmsg', '未知错误')
                        self.log_message(f"❌ 性别修改失败: {error_msg}")
                        return False
                except json.JSONDecodeError:
                    self.log_message(f"❌ 性别修改响应解析失败")
                    return False
            else:
                self.log_message(f"❌ 性别修改请求失败: HTTP {response.status_code}")
                return False

        except Exception as e:
            self.log_message(f"❌ 性别修改异常: {e}")
            return False



    def _update_nickname(self, account: AccountInfo, nickname: str):
        """修改昵称的线程函数"""
        try:
            self.log_message(f"🔄 正在修改账号 {account.uid} 的昵称为: {nickname}")

            url = "https://api.weibo.cn/2/users/update"
            headers = {
                "Host": "api.weibo.cn",
                "X-Sessionid": "FF428FE1-F3B7-4816-ABEB-6793B384E835",
                "Content-Type": "application/x-www-form-urlencoded",
                "Accept": "*/*",
                "Accept-Encoding": "gzip, deflate, br",
                "User-Agent": "WeiboOverseas/6.7.9 (iPhone; iOS 18.5; Scale/3.00)",
                "Accept-Language": "zh-Hans-GB;q=1, zh-Hant-GB;q=0.9, yue-Hant-GB;q=0.8"
            }

            # 设置Cookie（SUB使用GSID）
            cookies = {
                "SUB": account.gsid
            }

            # 构建请求数据
            data = {
                "aid": account.aid,
                "c": "weicoabroad",
                "from": "12DC193010",
                "screen_name": nickname,
                "gsid": account.gsid,
                "lang": "zh_CN",
                "s": account.s_value,
                "ua": "iPhone13,4_iOS18.5_Weibo_intl._6790_wifi__iphone__os18.5",
                "v_p": "59"
            }

            response = requests.post(url, headers=headers, cookies=cookies, data=data, timeout=15)

            if response.status_code == 200:
                try:
                    result = response.json()
                    if 'screen_name' in result:
                        self.log_message(f"✅ 昵称修改成功: {account.nickname} -> {result.get('screen_name')}")
                        # 更新本地信息
                        account.nickname = result.get('screen_name')
                        account.current_profile['screen_name'] = result.get('screen_name')
                        # 刷新表格显示
                        self.root.after(0, self.refresh_table)
                        # 刷新显示
                        self.root.after(0, self._update_profile_display, account.current_profile)
                        return True
                    else:
                        error_msg = result.get('errmsg', '未知错误')
                        self.log_message(f"❌ 昵称修改失败: {error_msg}")
                        return False
                except json.JSONDecodeError:
                    self.log_message(f"❌ 昵称修改响应解析失败")
                    return False
            else:
                self.log_message(f"❌ 昵称修改请求失败: HTTP {response.status_code}")
                return False

        except Exception as e:
            self.log_message(f"❌ 昵称修改异常: {e}")
            return False



    def _update_description(self, account: AccountInfo, description: str):
        """修改简介的线程函数"""
        try:
            self.log_message(f"🔄 正在修改账号 {account.uid} 的简介")

            url = "https://api.weibo.cn/2/users/update"
            headers = {
                "Host": "api.weibo.cn",
                "X-Sessionid": "FF428FE1-F3B7-4816-ABEB-6793B384E835",
                "Content-Type": "application/x-www-form-urlencoded",
                "Accept": "*/*",
                "Accept-Encoding": "gzip, deflate, br",
                "User-Agent": "WeiboOverseas/6.7.9 (iPhone; iOS 18.5; Scale/3.00)",
                "Accept-Language": "zh-Hans-GB;q=1, zh-Hant-GB;q=0.9, yue-Hant-GB;q=0.8"
            }

            # 设置Cookie（SUB使用GSID）
            cookies = {
                "SUB": account.gsid
            }

            # 构建请求数据
            data = {
                "aid": account.aid,
                "c": "weicoabroad",
                "from": "12DC193010",
                "description": description,
                "gsid": account.gsid,
                "lang": "zh_CN",
                "s": account.s_value,
                "ua": "iPhone13,4_iOS18.5_Weibo_intl._6790_wifi__iphone__os18.5",
                "v_p": "59"
            }

            response = requests.post(url, headers=headers, cookies=cookies, data=data, timeout=15)

            if response.status_code == 200:
                try:
                    result = response.json()
                    if 'screen_name' in result:
                        self.log_message(f"✅ 简介修改成功")
                        # 更新本地资料信息
                        account.current_profile['description'] = description
                        # 刷新显示
                        self.root.after(0, self._update_profile_display, account.current_profile)
                        return True
                    else:
                        error_msg = result.get('errmsg', '未知错误')
                        self.log_message(f"❌ 简介修改失败: {error_msg}")
                        return False
                except json.JSONDecodeError:
                    self.log_message(f"❌ 简介修改响应解析失败")
                    return False
            else:
                self.log_message(f"❌ 简介修改请求失败: HTTP {response.status_code}")
                return False

        except Exception as e:
            self.log_message(f"❌ 简介修改异常: {e}")
            return False



    def _update_avatar(self, account: AccountInfo, image_path: str):
        """修改头像的线程函数"""
        try:
            import os
            if not os.path.exists(image_path):
                self.log_message("❌ 头像图片文件不存在")
                return False

            self.log_message(f"🔄 正在修改账号 {account.uid} 的头像...")

            url = "https://api.weibo.cn/2/account/avatar_upload"
            headers = {
                "User-Agent": "WeiboOverseas/6.7.9 (iPhone; iOS 18.5; Scale/3.00)",
                "X-Sessionid": "90BF173D-E93E-4412-B46C-2C626C1B1892",
                "Upload-Draft-Interop-Version": "6",
                "Accept-Language": "zh-Hans-GB;q=1, zh-Hant-GB;q=0.9, yue-Hant-GB;q=0.8",
                "Upload-Complete": "?1"
            }

            # 设置Cookie（SUB使用GSID）
            cookies = {
                "SUB": account.gsid
            }

            # 构建multipart form data
            data = {
                "aid": account.aid,
                "c": "weicoabroad",
                "from": "12DC193010",
                "gsid": account.gsid,
                "lang": "zh_CN",
                "s": account.s_value,
                "ua": "iPhone13,4_iOS18.5_Weibo_intl._6790_wifi__iphone__os18.5"
            }

            # 准备文件上传 - 使用 'image' 字段名
            with open(image_path, 'rb') as f:
                files = {
                    'image': (os.path.basename(image_path), f, 'image/jpeg')
                }

                response = requests.post(url, headers=headers, cookies=cookies, data=data, files=files, timeout=30)

            if response.status_code == 200:
                try:
                    result = response.json()
                    if 'avatar_large' in result or 'profile_image_url' in result:
                        self.log_message(f"✅ 头像修改成功")
                        # 更新本地资料信息
                        if 'profile_image_url' in result:
                            account.current_profile['profile_image_url'] = result['profile_image_url']
                        # 刷新显示
                        self.root.after(0, self._update_profile_display, account.current_profile)
                        return True
                    else:
                        error_msg = result.get('errmsg', '未知错误')
                        self.log_message(f"❌ 头像修改失败: {error_msg}")
                        return False
                except json.JSONDecodeError:
                    self.log_message(f"❌ 头像修改响应解析失败")
                    return False
            else:
                self.log_message(f"❌ 头像修改请求失败: HTTP {response.status_code}")
                return False

        except Exception as e:
            self.log_message(f"❌ 头像修改异常: {e}")
            return False

    def select_avatar_folder(self):
        """选择头像文件夹"""
        folder_path = filedialog.askdirectory(title="选择头像文件夹")

        if folder_path:
            import os
            # 检查文件夹中的图片文件
            image_files = []
            for file in os.listdir(folder_path):
                if file.lower().endswith(('.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp')):
                    image_files.append(os.path.join(folder_path, file))

            if image_files:
                self.selected_avatar_folder = folder_path
                self.avatar_folder_var.set(f"已选择: {os.path.basename(folder_path)} ({len(image_files)}张图片)")
                self.log_message(f"📁 已选择头像文件夹: {os.path.basename(folder_path)}, 包含 {len(image_files)} 张图片")
            else:
                messagebox.showwarning("警告", "选择的文件夹中没有找到图片文件")
        else:
            self.selected_avatar_folder = None

    def start_batch_update(self):
        """开始批量修改"""
        if not self.selected_accounts:
            messagebox.showwarning("警告", "请先选择要修改的账号")
            return

        # 检查就绪的账号数量
        ready_accounts = [acc for acc in self.selected_accounts if acc.status == "就绪"]
        if not ready_accounts:
            messagebox.showwarning("警告", "选中的账号中没有就绪的账号，请等待账号信息获取完成")
            return

        # 检查修改选项
        if not any([self.modify_gender_var.get(), self.modify_nickname_var.get(),
                   self.modify_avatar_var.get(), self.modify_description_var.get()]):
            messagebox.showwarning("警告", "请至少选择一项要修改的内容")
            return

        # 验证输入
        if self.modify_nickname_var.get():
            nicknames = self.get_nickname_list()
            if not nicknames:
                messagebox.showwarning("警告", "请输入昵称列表")
                return

        if self.modify_avatar_var.get():
            if not self.selected_avatar_folder:
                messagebox.showwarning("警告", "请选择头像文件夹")
                return

        # 确认开始
        modify_items = []
        if self.modify_gender_var.get():
            gender_text = "男" if self.batch_gender_var.get() == "m" else "女"
            modify_items.append(f"性别 -> {gender_text}")
        if self.modify_nickname_var.get():
            modify_items.append("昵称")
        if self.modify_avatar_var.get():
            modify_items.append("头像")
        if self.modify_description_var.get():
            modify_items.append("简介")

        result = messagebox.askyesno("确认批量修改",
                                   f"将对 {len(ready_accounts)} 个账号进行以下修改：\n" +
                                   "\n".join(f"• {item}" for item in modify_items) +
                                   f"\n\n确定要开始吗？")

        if result:
            self.batch_running = True
            self.batch_paused = False
            self.batch_thread = threading.Thread(target=self._batch_update_worker, daemon=True)
            self.batch_thread.start()

    def pause_batch_update(self):
        """暂停批量修改"""
        if self.batch_running:
            self.batch_paused = not self.batch_paused
            status = "已暂停" if self.batch_paused else "已恢复"
            self.log_message(f"⏸️ 批量修改{status}")

    def stop_batch_update(self):
        """停止批量修改"""
        if self.batch_running:
            self.batch_running = False
            self.batch_paused = False
            self.log_message("⏹️ 批量修改已停止")

    def get_nickname_list(self):
        """获取昵称列表"""
        text = self.nickname_text.get(1.0, tk.END).strip()
        if not text:
            return []
        return [line.strip() for line in text.split('\n') if line.strip()]

    def get_random_avatar(self):
        """从文件夹中随机选择一个头像"""
        if not self.selected_avatar_folder:
            return None

        import os
        import random

        image_files = []
        for file in os.listdir(self.selected_avatar_folder):
            if file.lower().endswith(('.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp')):
                image_files.append(os.path.join(self.selected_avatar_folder, file))

        return random.choice(image_files) if image_files else None

    def _batch_update_worker(self):
        """批量修改工作线程"""
        try:
            ready_accounts = [acc for acc in self.selected_accounts if acc.status == "就绪"]
            total_accounts = len(ready_accounts)

            # 获取昵称列表
            nicknames = self.get_nickname_list() if self.modify_nickname_var.get() else []

            # 获取统一简介
            batch_description = self.batch_description_text.get(1.0, tk.END).strip() if self.modify_description_var.get() else ""

            self.update_status(f"开始批量修改 {total_accounts} 个账号...")

            for i, account in enumerate(ready_accounts):
                if not self.batch_running:
                    break

                # 等待暂停解除
                while self.batch_paused and self.batch_running:
                    time.sleep(0.1)

                if not self.batch_running:
                    break

                # 更新进度
                progress = (i / total_accounts) * 100
                self.root.after(0, self._update_progress, f"处理账号 {i+1}/{total_accounts}: {account.uid}", progress)

                self.update_status(f"处理账号 {i+1}/{total_accounts}: {account.uid} ({account.nickname})")

                success_count = 0
                total_operations = sum([self.modify_gender_var.get(), self.modify_nickname_var.get(),
                                      self.modify_avatar_var.get(), self.modify_description_var.get()])

                # 修改性别
                if self.modify_gender_var.get():
                    if self._update_gender(account, self.batch_gender_var.get()):
                        success_count += 1
                    time.sleep(0.2)

                # 修改昵称
                if self.modify_nickname_var.get() and nicknames:
                    nickname_index = i % len(nicknames)
                    nickname = nicknames[nickname_index]
                    if self._update_nickname(account, nickname):
                        success_count += 1
                    time.sleep(0.2)

                # 修改头像
                if self.modify_avatar_var.get():
                    avatar_path = self.get_random_avatar()
                    if avatar_path and self._update_avatar(account, avatar_path):
                        success_count += 1
                    time.sleep(0.5)

                # 修改简介
                if self.modify_description_var.get() and batch_description:
                    if self._update_description(account, batch_description):
                        success_count += 1
                    time.sleep(0.2)

                # 记录结果并刷新表格
                if success_count == total_operations:
                    self.update_status(f"账号 {account.uid} 修改完成 ({success_count}/{total_operations})")
                else:
                    self.update_status(f"账号 {account.uid} 部分修改成功 ({success_count}/{total_operations})")

                # 刷新表格显示最新状态
                self.root.after(0, self.refresh_table)

                # 账号间间隔
                if i < total_accounts - 1:
                    time.sleep(0.3)

            # 完成
            if self.batch_running:
                self.root.after(0, self._update_progress, f"批量修改完成！处理了 {total_accounts} 个账号", 100)
                self.update_status(f"批量修改完成！共处理 {total_accounts} 个账号")
            else:
                self.root.after(0, self._update_progress, "批量修改已停止", 0)

            self.batch_running = False
            self.batch_paused = False

        except Exception as e:
            self.log_message(f"❌ 批量修改异常: {e}")
            self.batch_running = False
            self.batch_paused = False
            self.root.after(0, self._update_progress, f"批量修改出错: {e}", 0)

    def _update_progress(self, message: str, progress: float):
        """更新进度显示"""
        if progress > 0 and progress < 100:
            formatted_message = f"{message} ({progress:.1f}%)"
        else:
            formatted_message = message

        self.progress_var.set(formatted_message)
        self.progress_bar['value'] = progress


def main():
    """主程序入口"""
    root = tk.Tk()
    WeiboProfileEditor(root)  # 创建应用实例

    # 设置窗口居中
    root.update_idletasks()
    width = root.winfo_width()
    height = root.winfo_height()
    x = (root.winfo_screenwidth() // 2) - (width // 2)
    y = (root.winfo_screenheight() // 2) - (height // 2)
    root.geometry(f'{width}x{height}+{x}+{y}')

    root.mainloop()


if __name__ == "__main__":
    main()
